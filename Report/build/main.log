This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.3)  22 SEP 2025 19:32
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/Report/main.tex
(/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/Report/main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen144
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks21
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks22
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/breakcites/breakcites.sty) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count283
\Gm@cntv=\count284
\c@Gm@tempcnt=\count285
\Gm@bindingoffset=\dimen153
\Gm@wd@mp=\dimen154
\Gm@odd@mp=\dimen155
\Gm@even@mp=\dimen156
\Gm@layoutwidth=\dimen157
\Gm@layoutheight=\dimen158
\Gm@layouthoffset=\dimen159
\Gm@layoutvoffset=\dimen160
\Gm@dimlist=\toks25
) (/usr/local/texlive/2025/texmf-dist/tex/latex/sidecap/sidecap.sty
Package: sidecap 2023/01/24 v1.7a SideCap Package (RN/HjG)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\SC@BOXWD=\dimen161
\SC@CAPWD=\dimen162
\SC@tempdima=\dimen163
\SC@tempdimb=\dimen164
\c@SC@C=\count286
\SC@BOX=\box54
) (/usr/local/texlive/2025/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2024/03/21 v3.20 programmable bibliographies (PK/MW)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count287
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count288
 (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
\c@tabx@nest=\count289
\c@listtotal=\count290
\c@listcount=\count291
\c@liststart=\count292
\c@liststop=\count293
\c@citecount=\count294
\c@citetotal=\count295
\c@multicitecount=\count296
\c@multicitetotal=\count297
\c@instcount=\count298
\c@maxnames=\count299
\c@minnames=\count300
\c@maxitems=\count301
\c@minitems=\count302
\c@citecounter=\count303
\c@maxcitecounter=\count304
\c@savedcitecounter=\count305
\c@uniquelist=\count306
\c@uniquename=\count307
\c@refsection=\count308
\c@refsegment=\count309
\c@maxextratitle=\count310
\c@maxextratitleyear=\count311
\c@maxextraname=\count312
\c@maxextradate=\count313
\c@maxextraalpha=\count314
\c@abbrvpenalty=\count315
\c@highnamepenalty=\count316
\c@lownamepenalty=\count317
\c@maxparens=\count318
\c@parenlevel=\count319
\blx@tempcnta=\count320
\blx@tempcntb=\count321
\blx@tempcntc=\count322
\c@blx@maxsection=\count323
\blx@maxsegment@0=\count324
\blx@notetype=\count325
\blx@parenlevel@text=\count326
\blx@parenlevel@foot=\count327
\blx@sectionciteorder@0=\count328
\blx@sectionciteorderinternal@0=\count329
\blx@entrysetcounter=\count330
\blx@biblioinstance=\count331
\labelnumberwidth=\skip54
\labelalphawidth=\skip55
\biblabelsep=\skip56
\bibitemsep=\skip57
\bibnamesep=\skip58
\bibinitsep=\skip59
\bibparsep=\skip60
\bibhang=\skip61
\blx@bcfin=\read2
\blx@bcfout=\write3
\blx@langwohyphens=\language90
\c@mincomprange=\count332
\c@maxcomprange=\count333
\c@mincompwidth=\count334
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2024/03/21 v3.20 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'apa.dbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-apa/apa.dbx
File: apa.dbx 2023/03/20 v9.17 APA biblatex style data model
)
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count335
\c@savedafterword=\count336
\c@annotator=\count337
\c@savedannotator=\count338
\c@author=\count339
\c@savedauthor=\count340
\c@bookauthor=\count341
\c@savedbookauthor=\count342
\c@commentator=\count343
\c@savedcommentator=\count344
\c@editor=\count345
\c@savededitor=\count346
\c@editora=\count347
\c@savededitora=\count348
\c@editorb=\count349
\c@savededitorb=\count350
\c@editorc=\count351
\c@savededitorc=\count352
\c@foreword=\count353
\c@savedforeword=\count354
\c@holder=\count355
\c@savedholder=\count356
\c@introduction=\count357
\c@savedintroduction=\count358
\c@namea=\count359
\c@savednamea=\count360
\c@nameb=\count361
\c@savednameb=\count362
\c@namec=\count363
\c@savednamec=\count364
\c@translator=\count365
\c@savedtranslator=\count366
\c@shortauthor=\count367
\c@savedshortauthor=\count368
\c@shorteditor=\count369
\c@savedshorteditor=\count370
\c@narrator=\count371
\c@savednarrator=\count372
\c@execproducer=\count373
\c@savedexecproducer=\count374
\c@execdirector=\count375
\c@savedexecdirector=\count376
\c@with=\count377
\c@savedwith=\count378
\c@labelname=\count379
\c@savedlabelname=\count380
\c@institution=\count381
\c@savedinstitution=\count382
\c@lista=\count383
\c@savedlista=\count384
\c@listb=\count385
\c@savedlistb=\count386
\c@listc=\count387
\c@savedlistc=\count388
\c@listd=\count389
\c@savedlistd=\count390
\c@liste=\count391
\c@savedliste=\count392
\c@listf=\count393
\c@savedlistf=\count394
\c@location=\count395
\c@savedlocation=\count396
\c@organization=\count397
\c@savedorganization=\count398
\c@origlocation=\count399
\c@savedoriglocation=\count400
\c@origpublisher=\count401
\c@savedorigpublisher=\count402
\c@publisher=\count403
\c@savedpublisher=\count404
\c@language=\count405
\c@savedlanguage=\count406
\c@origlanguage=\count407
\c@savedoriglanguage=\count408
\c@citation=\count409
\c@savedcitation=\count410
\c@pageref=\count411
\c@savedpageref=\count412
\shorthandwidth=\skip62
\shortjournalwidth=\skip63
\shortserieswidth=\skip64
\shorttitlewidth=\skip65
\shortauthorwidth=\skip66
\shorteditorwidth=\skip67
\locallabelnumberwidth=\skip68
\locallabelalphawidth=\skip69
\localshorthandwidth=\skip70
\localshortjournalwidth=\skip71
\localshortserieswidth=\skip72
\localshorttitlewidth=\skip73
\localshortauthorwidth=\skip74
\localshorteditorwidth=\skip75
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
\c@textcitecount=\count413
\c@textcitetotal=\count414
\c@textcitemaxnames=\count415
\c@biburlbigbreakpenalty=\count416
\c@biburlbreakpenalty=\count417
\c@biburlnumpenalty=\count418
\c@biburlucpenalty=\count419
\c@biburllcpenalty=\count420
\biburlbigskip=\muskip19
\biburlnumskip=\muskip20
\biburlucskip=\muskip21
\biburllcskip=\muskip22
\c@smartand=\count421
)
Package biblatex Info: Trying to load bibliography style 'apa'...
Package biblatex Info: ... file 'apa.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-apa/apa.bbx
File: apa.bbx 2023/03/20 v9.17 APA biblatex references style
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count422
\c@bbx:relatedtotal=\count423
)
Package biblatex Info: Delimiter 'nameyeardelim' in context 'bib' already defined, overwriting.
Package biblatex Info: Delimiter 'nameyeardelim' in context 'biblist' already defined, overwriting.
Package biblatex Info: Delimiter 'nonameyeardelim' in context 'bib' already defined, overwriting.
Package biblatex Info: Delimiter 'nonameyeardelim' in context 'biblist' already defined, overwriting.
)
Package biblatex Info: Trying to load citation style 'apa'...
Package biblatex Info: ... file 'apa.cbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-apa/apa.cbx
File: apa.cbx 2023/03/20 v9.17 APA biblatex citation style
Package biblatex Info: Delimiter 'nameyeardelim' in context '' already defined, overwriting.
Package biblatex Info: Delimiter 'andothersdelim' in context '' already defined, overwriting.
Package biblatex Info: Redefining '\fullcite'.
Package biblatex Info: Redefining '\citeauthor'.
Package biblatex Info: Redefining '\citeyear'.
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\cites'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcites'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\footcitetexts'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\smartcites'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
)
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Document encoding is UTF8 ....
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count424
\l__pdf_internal_box=\box55
))
Package biblatex Info: ... and expl3
(biblatex)             2025-01-18 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: blx-case-expl3 2024/03/21 v3.20 expl3 case changing code for biblatex
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count425
) (/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen165
\Hy@linkcounter=\count426
\Hy@pagecounter=\count427
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count428
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count429
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen166
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count430
\Field@Width=\dimen167
\Fld@charsize=\dimen168
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count431
\c@Item=\count432
\c@Hfootnote=\count433
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count434
\c@bookmark@seq@number=\count435
 (/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip76
)
Package hyperref Info: Option `colorlinks' set `true' on input line 42.
\@quotelevel=\count436
\@quotereset=\count437
 (build/main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 51.
LaTeX Font Info:    ... okay on input line 51.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count438
\scratchdimen=\dimen169
\scratchbox=\box56
\nofMPsegments=\count439
\nofMParguments=\count440
\everyMPshowfont=\toks26
\MPscratchCnt=\count441
\MPscratchDim=\dimen170
\MPnumerator=\count442
\makeMPintoPDFobject=\count443
\everyMPtoPDFconversion=\toks27
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip77
\RaggedLeftLeftskip=\skip78
\RaggedRightLeftskip=\skip79
\CenteringRightskip=\skip80
\RaggedLeftRightskip=\skip81
\RaggedRightRightskip=\skip82
\CenteringParfillskip=\skip83
\RaggedLeftParfillskip=\skip84
\RaggedRightParfillskip=\skip85
\JustifyingParfillskip=\skip86
\CenteringParindent=\skip87
\RaggedLeftParindent=\skip88
\RaggedRightParindent=\skip89
\JustifyingParindent=\skip90
)
Package biblatex Info: Trying to load language 'english' -> 'english-apa'...
Package biblatex Info: ... file 'english-apa.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-apa/english-apa.lbx
File: english-apa.lbx 2023/03/20 v9.17 APA biblatex localisation
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
)
Package biblatex Info: Trying to load language 'american' -> 'american-apa'...
Package biblatex Info: ... file 'american-apa.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-apa/american-apa.lbx
File: american-apa.lbx 2023/03/20 v9.17 APA biblatex localisation
Package biblatex Info: Trying to load language 'american'...
Package biblatex Info: ... file 'american.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/american.lbx
File: american.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
))))
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout3 = `main.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'main.bbl' found.
 (build/main.bbl)
Package biblatex Info: Reference section=0 on input line 51.
Package biblatex Info: Reference segment=0 on input line 51.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 51.
 (build/main.out) (build/main.out)
\@outlinefile=\write4
\openout4 = `main.out'.

 (./chapters/titlepage.tex
<images/Nanyang_Technological_University.png, id=60, 1927.2pt x 690.58pt>
File: images/Nanyang_Technological_University.png Graphic file (type png)
<use images/Nanyang_Technological_University.png>
Package pdftex.def Info: images/Nanyang_Technological_University.png  used on input line 9.
(pdftex.def)             Requested size: 328.82707pt x 117.82907pt.


[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map} <./images/Nanyang_Technological_University.png>]) (./chapters/abstract.tex)

[1] (build/main.lof)
\tf@lof=\write5
\openout5 = `main.lof'.



[2

] (build/main.lot)
\tf@lot=\write6
\openout6 = `main.lot'.



[3

] (build/main.toc)
\tf@toc=\write7
\openout7 = `main.toc'.



[4

]
Chapter 1.
(./chapters/chapter01.tex
<images/chap01_images/PaleBlueDot.png, id=108, 552.0625pt x 365.365pt>
File: images/chap01_images/PaleBlueDot.png Graphic file (type png)
<use images/chap01_images/PaleBlueDot.png>
Package pdftex.def Info: images/chap01_images/PaleBlueDot.png  used on input line 11.
(pdftex.def)             Requested size: 234.8775pt x 155.4481pt.
)


pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.79 \chapter
             {Some Literature review} [1

 <./images/chap01_images/PaleBlueDot.png>]
Chapter 2.
(./chapters/chapter02.tex)

[2

]
Chapter 3.
(./chapters/chapter03.tex)

[3

]
Chapter 4.
(./chapters/chapter04.tex
LaTeX Font Info:    Trying to load font information for OML+cmr on input line 6.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/omlcmr.fd
File: omlcmr.fd 2024/11/19 v2.5n Standard LaTeX font definitions
)
LaTeX Font Info:    Font shape `OML/cmr/m/n' in size <10> not available
(Font)              Font shape `OML/cmm/m/it' tried instead on input line 6.
)

[4

]
Chapter 5.
(./chapters/chapter05.tex)

[5

]

[6

] (build/main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: 83DBA734B1103F68E8DB339FECAB7299;1700.
Package logreq Info: Writing requests to 'main.run.xml'.
\openout1 = `main.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 18174 strings out of 473190
 321925 string characters out of 5715801
 1250432 words of memory out of 5000000
 41182 multiletter control sequences out of 15000+600000
 562367 words of font info for 48 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 66i,6n,81p,783b,5050s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb>
Output written on build/main.pdf (11 pages, 455448 bytes).
PDF statistics:
 178 PDF objects out of 1000 (max. 8388607)
 147 compressed objects within 2 object streams
 32 named destinations out of 1000 (max. 500000)
 123 words of extra memory for PDF output out of 10000 (max. 10000000)

