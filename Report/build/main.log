This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.3)  22 SEP 2025 21:10
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/Report/main.tex
(/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/Report/main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen144
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks21
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks22
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/physics/physics.sty
Package: physics 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count283
\l__pdf_internal_box=\box54
))
Package: xparse 2024-08-16 L3 Experimental document command parser
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/breakcites/breakcites.sty) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count284
\Gm@cntv=\count285
\c@Gm@tempcnt=\count286
\Gm@bindingoffset=\dimen153
\Gm@wd@mp=\dimen154
\Gm@odd@mp=\dimen155
\Gm@even@mp=\dimen156
\Gm@layoutwidth=\dimen157
\Gm@layoutheight=\dimen158
\Gm@layouthoffset=\dimen159
\Gm@layoutvoffset=\dimen160
\Gm@dimlist=\toks25
) (/usr/local/texlive/2025/texmf-dist/tex/latex/sidecap/sidecap.sty
Package: sidecap 2023/01/24 v1.7a SideCap Package (RN/HjG)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\SC@BOXWD=\dimen161
\SC@CAPWD=\dimen162
\SC@tempdima=\dimen163
\SC@tempdimb=\dimen164
\c@SC@C=\count287
\SC@BOX=\box55
) (/usr/local/texlive/2025/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen165
\captionmargin=\dimen166
\caption@leftmargin=\dimen167
\caption@rightmargin=\dimen168
\caption@width=\dimen169
\caption@indent=\dimen170
\caption@parindent=\dimen171
\caption@hangindent=\dimen172
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count288
\c@continuedfloat=\count289
Package caption Info: sidecap package is loaded.
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count290
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count291
) (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count292
\float@exts=\toks26
\float@box=\box56
\@float@everytoks=\toks27
\@floatcapt=\box57
) (/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen173
\enit@toks=\toks28
\enit@inbox=\box58
\enit@count@id=\count293
\enitdp@description=\count294
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip55
\f@nch@offset@elh=\skip56
\f@nch@offset@erh=\skip57
\f@nch@offset@olh=\skip58
\f@nch@offset@orh=\skip59
\f@nch@offset@elf=\skip60
\f@nch@offset@erf=\skip61
\f@nch@offset@olf=\skip62
\f@nch@offset@orf=\skip63
\f@nch@height=\skip64
\f@nch@footalignment=\skip65
\f@nch@widthL=\skip66
\f@nch@widthC=\skip67
\f@nch@widthR=\skip68
\@temptokenb=\toks29
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks30
\pgfutil@tempdima=\dimen174
\pgfutil@tempdimb=\dimen175
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box59
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks31
\pgfkeys@temptoks=\toks32
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks33
))
\pgf@x=\dimen176
\pgf@y=\dimen177
\pgf@xa=\dimen178
\pgf@ya=\dimen179
\pgf@xb=\dimen180
\pgf@yb=\dimen181
\pgf@xc=\dimen182
\pgf@yc=\dimen183
\pgf@xd=\dimen184
\pgf@yd=\dimen185
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count295
\c@pgf@countb=\count296
\c@pgf@countc=\count297
\c@pgf@countd=\count298
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36
\pgf@sys@id@count=\count299
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count300
\pgfsyssoftpath@bigbuffer@items=\count301
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen186
\pgfmath@count=\count302
\pgfmath@box=\box60
\pgfmath@toks=\toks37
\pgfmath@stack@operand=\toks38
\pgfmath@stack@operation=\toks39
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count303
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen187
\pgf@picmaxx=\dimen188
\pgf@picminy=\dimen189
\pgf@picmaxy=\dimen190
\pgf@pathminx=\dimen191
\pgf@pathmaxx=\dimen192
\pgf@pathminy=\dimen193
\pgf@pathmaxy=\dimen194
\pgf@xx=\dimen195
\pgf@xy=\dimen196
\pgf@yx=\dimen197
\pgf@yy=\dimen198
\pgf@zx=\dimen199
\pgf@zy=\dimen256
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen257
\pgf@path@lasty=\dimen258
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen259
\pgf@shorten@start@additional=\dimen260
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count304
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen261
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen262
\pgf@pt@y=\dimen263
\pgf@pt@temp=\dimen264
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen265
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen266
\pgf@sys@shading@range@num=\count305
\pgf@shadingcount=\count306
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen267
\pgf@nodesepend=\dimen268
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen269
\pgffor@skip=\dimen270
\pgffor@stack=\toks40
\pgffor@toks=\toks41
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count307
\pgfplotmarksize=\dimen271
)
\tikz@lastx=\dimen272
\tikz@lasty=\dimen273
\tikz@lastxsaved=\dimen274
\tikz@lastysaved=\dimen275
\tikz@lastmovetox=\dimen276
\tikz@lastmovetoy=\dimen277
\tikzleveldistance=\dimen278
\tikzsiblingdistance=\dimen279
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count308
\tikznumberofchildren=\count309
\tikznumberofcurrentchild=\count310
\tikz@fig@count=\count311
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count312
\pgfmatrixcurrentcolumn=\count313
\pgf@matrix@numberofcolumns=\count314
)
\tikz@expandcount=\count315
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2024/03/21 v3.20 programmable bibliographies (PK/MW)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count316
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count317
 (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
\c@tabx@nest=\count318
\c@listtotal=\count319
\c@listcount=\count320
\c@liststart=\count321
\c@liststop=\count322
\c@citecount=\count323
\c@citetotal=\count324
\c@multicitecount=\count325
\c@multicitetotal=\count326
\c@instcount=\count327
\c@maxnames=\count328
\c@minnames=\count329
\c@maxitems=\count330
\c@minitems=\count331
\c@citecounter=\count332
\c@maxcitecounter=\count333
\c@savedcitecounter=\count334
\c@uniquelist=\count335
\c@uniquename=\count336
\c@refsection=\count337
\c@refsegment=\count338
\c@maxextratitle=\count339
\c@maxextratitleyear=\count340
\c@maxextraname=\count341
\c@maxextradate=\count342
\c@maxextraalpha=\count343
\c@abbrvpenalty=\count344
\c@highnamepenalty=\count345
\c@lownamepenalty=\count346
\c@maxparens=\count347
\c@parenlevel=\count348
\blx@tempcnta=\count349
\blx@tempcntb=\count350
\blx@tempcntc=\count351
\c@blx@maxsection=\count352
\blx@maxsegment@0=\count353
\blx@notetype=\count354
\blx@parenlevel@text=\count355
\blx@parenlevel@foot=\count356
\blx@sectionciteorder@0=\count357
\blx@sectionciteorderinternal@0=\count358
\blx@entrysetcounter=\count359
\blx@biblioinstance=\count360
\labelnumberwidth=\skip69
\labelalphawidth=\skip70
\biblabelsep=\skip71
\bibitemsep=\skip72
\bibnamesep=\skip73
\bibinitsep=\skip74
\bibparsep=\skip75
\bibhang=\skip76
\blx@bcfin=\read3
\blx@bcfout=\write4
\blx@langwohyphens=\language90
\c@mincomprange=\count361
\c@maxcomprange=\count362
\c@mincompwidth=\count363
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2024/03/21 v3.20 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'phys.dbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.dbx
File: phys.dbx 2024/07/26 v1.1c biblatex database model extension
)
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count364
\c@savedafterword=\count365
\c@annotator=\count366
\c@savedannotator=\count367
\c@author=\count368
\c@savedauthor=\count369
\c@bookauthor=\count370
\c@savedbookauthor=\count371
\c@commentator=\count372
\c@savedcommentator=\count373
\c@editor=\count374
\c@savededitor=\count375
\c@editora=\count376
\c@savededitora=\count377
\c@editorb=\count378
\c@savededitorb=\count379
\c@editorc=\count380
\c@savededitorc=\count381
\c@foreword=\count382
\c@savedforeword=\count383
\c@holder=\count384
\c@savedholder=\count385
\c@introduction=\count386
\c@savedintroduction=\count387
\c@namea=\count388
\c@savednamea=\count389
\c@nameb=\count390
\c@savednameb=\count391
\c@namec=\count392
\c@savednamec=\count393
\c@translator=\count394
\c@savedtranslator=\count395
\c@shortauthor=\count396
\c@savedshortauthor=\count397
\c@shorteditor=\count398
\c@savedshorteditor=\count399
\c@labelname=\count400
\c@savedlabelname=\count401
\c@institution=\count402
\c@savedinstitution=\count403
\c@lista=\count404
\c@savedlista=\count405
\c@listb=\count406
\c@savedlistb=\count407
\c@listc=\count408
\c@savedlistc=\count409
\c@listd=\count410
\c@savedlistd=\count411
\c@liste=\count412
\c@savedliste=\count413
\c@listf=\count414
\c@savedlistf=\count415
\c@location=\count416
\c@savedlocation=\count417
\c@organization=\count418
\c@savedorganization=\count419
\c@origlocation=\count420
\c@savedoriglocation=\count421
\c@origpublisher=\count422
\c@savedorigpublisher=\count423
\c@publisher=\count424
\c@savedpublisher=\count425
\c@language=\count426
\c@savedlanguage=\count427
\c@origlanguage=\count428
\c@savedoriglanguage=\count429
\c@pageref=\count430
\c@savedpageref=\count431
\shorthandwidth=\skip77
\shortjournalwidth=\skip78
\shortserieswidth=\skip79
\shorttitlewidth=\skip80
\shortauthorwidth=\skip81
\shorteditorwidth=\skip82
\locallabelnumberwidth=\skip83
\locallabelalphawidth=\skip84
\localshorthandwidth=\skip85
\localshortjournalwidth=\skip86
\localshortserieswidth=\skip87
\localshorttitlewidth=\skip88
\localshortauthorwidth=\skip89
\localshorteditorwidth=\skip90
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
\c@textcitecount=\count432
\c@textcitetotal=\count433
\c@textcitemaxnames=\count434
\c@biburlbigbreakpenalty=\count435
\c@biburlbreakpenalty=\count436
\c@biburlnumpenalty=\count437
\c@biburlucpenalty=\count438
\c@biburllcpenalty=\count439
\biburlbigskip=\muskip19
\biburlnumskip=\muskip20
\biburlucskip=\muskip21
\biburllcskip=\muskip22
\c@smartand=\count440
)
Package biblatex Info: Trying to load bibliography style 'phys'...
Package biblatex Info: ... file 'phys.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.bbx
File: phys.bbx 2024/07/26 v1.1c biblatex bibliography style
Package biblatex Info: Trying to load bibliography style 'numeric-comp'...
Package biblatex Info: ... file 'numeric-comp.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/numeric-comp.bbx
File: numeric-comp.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'numeric'...
Package biblatex Info: ... file 'numeric.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/numeric.bbx
File: numeric.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count441
\c@bbx:relatedtotal=\count442
))))
Package biblatex Info: Trying to load citation style 'phys'...
Package biblatex Info: ... file 'phys.cbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.cbx
File: phys.cbx 2024/07/26 v1.1c biblatex citation style
Package biblatex Info: Trying to load citation style 'numeric-comp'...
Package biblatex Info: ... file 'numeric-comp.cbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/cbx/numeric-comp.cbx
File: numeric-comp.cbx 2024/03/21 v3.20 biblatex citation style (PK/MW)
\c@cbx@tempcnta=\count443
\c@cbx@tempcntb=\count444
\c@cbx@tempcntc=\count445
\c@cbx@tempcntd=\count446
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\supercite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
Package biblatex Info: Redefining '\cites'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\smartcites'.
))
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Document encoding is UTF8 ....
Package biblatex Info: ... and expl3
(biblatex)             2025-01-18 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty
Package: blx-case-expl3 2024/03/21 v3.20 expl3 case changing code for biblatex
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count447
) (/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen280
\Hy@linkcounter=\count448
\Hy@pagecounter=\count449
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count450
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count451
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen281
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count452
\Field@Width=\dimen282
\Fld@charsize=\dimen283
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count453
\c@Item=\count454
\c@Hfootnote=\count455
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count456
\c@bookmark@seq@number=\count457

(/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip91
)
Package hyperref Info: Option `colorlinks' set `true' on input line 47.
\@quotelevel=\count458
\@quotereset=\count459
 (build/main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count460
\scratchdimen=\dimen284
\scratchbox=\box70
\nofMPsegments=\count461
\nofMParguments=\count462
\everyMPshowfont=\toks42
\MPscratchCnt=\count463
\MPscratchDim=\dimen285
\MPnumerator=\count464
\makeMPintoPDFobject=\count465
\everyMPtoPDFconversion=\toks43
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip92
\RaggedLeftLeftskip=\skip93
\RaggedRightLeftskip=\skip94
\CenteringRightskip=\skip95
\RaggedLeftRightskip=\skip96
\RaggedRightRightskip=\skip97
\CenteringParfillskip=\skip98
\RaggedLeftParfillskip=\skip99
\RaggedRightParfillskip=\skip100
\JustifyingParfillskip=\skip101
\CenteringParindent=\skip102
\RaggedLeftParindent=\skip103
\RaggedRightParindent=\skip104
\JustifyingParindent=\skip105
)
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout4 = `main.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'main.bbl' found.
 (build/main.bbl)
Package biblatex Info: Reference section=0 on input line 68.
Package biblatex Info: Reference segment=0 on input line 68.
Package hyperref Info: Link coloring ON on input line 68.
 (build/main.out) (build/main.out)
\@outlinefile=\write5
\openout5 = `main.out'.

 (./chapters/titlepage.tex
<images/Nanyang_Technological_University.png, id=272, 1927.2pt x 690.58pt>
File: images/Nanyang_Technological_University.png Graphic file (type png)
<use images/Nanyang_Technological_University.png>
Package pdftex.def Info: images/Nanyang_Technological_University.png  used on input line 9.
(pdftex.def)             Requested size: 328.82707pt x 117.82907pt.


[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map} <./images/Nanyang_Technological_University.png>]) (./chapters/abstract.tex
LaTeX Font Info:    Trying to load font information for U+msa on input line 17.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 17.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
))

[1{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}] (build/main.lof)
\tf@lof=\write6
\openout6 = `main.lof'.



[2

] (build/main.lot)
\tf@lot=\write7
\openout7 = `main.lot'.



[3

] (build/main.toc

[4

])
\tf@toc=\write8
\openout8 = `main.toc'.




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[5]
Chapter 1.
(./chapters/chapter01.tex
<images/lattice.png, id=412, 385.44pt x 385.44pt>
File: images/lattice.png Graphic file (type png)
<use images/lattice.png>
Package pdftex.def Info: images/lattice.png  used on input line 17.
(pdftex.def)             Requested size: 187.89914pt x 187.89682pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 24.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 24.




pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.28 E
      xperimental studies of SrCu$_2$(BO$_3$)$_2$ have revealed fascinating ... [1

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[2 <./images/lattice.png (PNG copy)>])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[3]
Chapter 2.
(./chapters/chapter02.tex
<images/phase.png, id=447, 906.38625pt x 355.3275pt>
File: images/phase.png Graphic file (type png)
<use images/phase.png>
Package pdftex.def Info: images/phase.png  used on input line 25.
(pdftex.def)             Requested size: 281.85587pt x 110.49196pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[4

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[5 <./images/phase.png>]
<images/prophase.jpeg, id=471, 1988.42876pt x 1116.17pt>
File: images/prophase.jpeg Graphic file (type jpg)
<use images/prophase.jpeg>
Package pdftex.def Info: images/prophase.jpeg  used on input line 83.
(pdftex.def)             Requested size: 234.8775pt x 131.83975pt.



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[6 <./images/prophase.jpeg>]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[7])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[8]
Chapter 3.
(./chapters/chapter03.tex

[9

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[10]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[11]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[12])
Overfull \hbox (2.4035pt too wide) in paragraph at lines 188--100
\OT1/cmr/m/n/10.95 Shastry-Sutherland model across the pro-posed phase di-a-gram, pro-vid-ing ac-cess to pa-ram-e-ter regimes
 []




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[13]
Chapter 4.
(./chapters/chapter04.tex
<images/energy1.png, id=551, 859.2903pt x 570.933pt>
File: images/energy1.png Graphic file (type png)
<use images/energy1.png>
Package pdftex.def Info: images/energy1.png  used on input line 13.
(pdftex.def)             Requested size: 225.4804pt x 149.81552pt.
<images/energy2.png, id=552, 859.2903pt x 570.933pt>
File: images/energy2.png Graphic file (type png)
<use images/energy2.png>
Package pdftex.def Info: images/energy2.png  used on input line 19.
(pdftex.def)             Requested size: 225.4804pt x 149.81552pt.


[14

 <./images/energy1.png> <./images/energy2.png>]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[15]
<images/image1.png, id=567, 715.473pt x 426.393pt>
File: images/image1.png Graphic file (type png)
<use images/image1.png>
Package pdftex.def Info: images/image1.png  used on input line 123.
(pdftex.def)             Requested size: 234.8775pt x 139.97491pt.
<images/image2.png, id=568, 715.473pt x 426.393pt>
File: images/image2.png Graphic file (type png)
<use images/image2.png>
Package pdftex.def Info: images/image2.png  used on input line 129.
(pdftex.def)             Requested size: 234.8775pt x 139.97491pt.
<images/image3.png, id=569, 715.473pt x 426.393pt>
File: images/image3.png Graphic file (type png)
<use images/image3.png>
Package pdftex.def Info: images/image3.png  used on input line 135.
(pdftex.def)             Requested size: 234.8775pt x 139.97491pt.



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[16]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[17 <./images/image1.png> <./images/image2.png> <./images/image3.png>]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[18])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[19]
Chapter 5.
(./chapters/chapter05.tex

[20

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[21]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[22]
Overfull \hbox (2.14493pt too wide) in paragraph at lines 147--149
[]\OT1/cmr/bx/n/10.95 Advanced Op-ti-miza-tion Al-go-rithms\OT1/cmr/m/n/10.95 : Im-ple-ment state-of-the-art op-ti-miza-tion tech-niques: 
 []




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[23]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[24])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[25]

[26

] (build/main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: CFEA0497078A0E045485246F1A1BB85A;14619.
Package logreq Info: Writing requests to 'main.run.xml'.
\openout1 = `main.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 31479 strings out of 473190
 591995 string characters out of 5715801
 1397057 words of memory out of 5000000
 54057 multiletter control sequences out of 15000+600000
 571654 words of font info for 83 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 84i,14n,93p,921b,1536s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmib10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmmib8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1095.pfb>
Output written on build/main.pdf (32 pages, 1755155 bytes).
PDF statistics:
 789 PDF objects out of 1000 (max. 8388607)
 691 compressed objects within 7 object streams
 200 named destinations out of 1000 (max. 500000)
 586 words of extra memory for PDF output out of 10000 (max. 10000000)

