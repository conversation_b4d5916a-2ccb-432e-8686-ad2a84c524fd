
import numpy as np
import netket as nk
from netket.operator.spin import sigmax, sigmay, sigmaz
from scipy.sparse.linalg import eigsh
import warnings
warnings.filterwarnings("ignore", category=nk.errors.HolomorphicUndeclaredWarning)



# Utility Functions
def compute_ground_state(H, hi):
    """
    Compute ground state energy and wave function for a given Hamiltonian
    
    Args:
        H: The Hamiltonian operator
        hi: Hilbert space
        
    Returns:
        ground_state_energy: Lowest eigenvalue
        ground_state: Corresponding eigenvector
    """
    sp_h = H.to_sparse()
    eig_vals, eig_vecs = eigsh(sp_h, k=1, which="SA")
    ground_state_energy = eig_vals[0]
    ground_state = eig_vecs[:, 0]
    return ground_state_energy, ground_state

def qmc_energy_estimate(ground_state, L, n_samples, diag_multiplier, offdiag_multiplier):
    """
    Estimate energy using Monte Carlo sampling
    
    - First samples from Hilbert space according to |ψ|²
    - For each sampled configuration, calculates the local energy:
         Local energy = (diagonal part) + (off-diagonal part)
    - Diagonal part: For each nearest-neighbor pair i, contributes diag_multiplier * config[i]*config[(i+1)%L]
    - Off-diagonal part: For each spin i in flipped configuration, contributes offdiag_multiplier * (ψ(flipped)/ψ(original))
    
    Args:
        ground_state: Ground state wave function
        L: System size
        n_samples: Number of Monte Carlo samples
        diag_multiplier: Coefficient for diagonal terms
        offdiag_multiplier: Coefficient for off-diagonal terms
        
    Returns:
        qmc_energy: Mean energy from Monte Carlo sampling
        qmc_error: Standard error of the estimate
    """
    dim = 2**L
    prob_weights = np.abs(ground_state)**2
    prob_weights /= np.sum(prob_weights)
    sample_indices = np.random.choice(dim, size=n_samples, p=prob_weights)
    
    energy_samples = []
    for idx in sample_indices:
        bin_repr = format(idx, f"0{L}b")
        config = np.array([1 - 2 * int(bit) for bit in bin_repr])
        local_energy = 0
        
        # Diagonal part (e.g., nearest-neighbor interaction terms)
        for i in range(L):
            local_energy += diag_multiplier * config[i] * config[(i + 1) % L]
        
        # Off-diagonal part (e.g., spin-flip terms)
        for i in range(L):
            flipped_config = config.copy()
            flipped_config[i] *= -1
            # Generate binary string for flipped configuration, then convert to integer index
            flipped_idx_str = ''.join(['1' if spin == -1 else '0' for spin in flipped_config])
            flipped_idx_int = int(flipped_idx_str, 2)
            psi_ratio = ground_state[flipped_idx_int] / ground_state[idx]
            local_energy += offdiag_multiplier * psi_ratio

        
        energy_samples.append(local_energy)
    
    qmc_energy = np.mean(energy_samples)
    qmc_error = np.std(energy_samples) / np.sqrt(n_samples)
    return qmc_energy, qmc_error

def tfim_model_sign(L=8, J=1.0, h=1.0, n_samples=100000):
    """
    QMC simulation of 1D transverse field Ising model, demonstrating sign problem detection
    Hamiltonian: H = J ∑ σ_i^z σ_{i+1}^z + h ∑ σ_i^x

    Args:
        L: System size (chain length)
        J: Nearest-neighbor coupling (positive for antiferromagnetic, negative for ferromagnetic)
        h: Transverse field strength
        n_samples: Number of Monte Carlo samples
        
    Returns:
        exact_energy: Energy from exact diagonalization
        qmc_energy: Energy from QMC estimation
        qmc_error: Error in QMC estimation
    """
    # Define the Hilbert space (spin 1/2)
    hi = nk.hilbert.Spin(s=1/2, N=L)
    
    # Construct transverse field Ising model Hamiltonian
    H = sum([J * sigmaz(hi, i) * sigmaz(hi, (i + 1) % L) for i in range(L)])
    H += sum([h * sigmax(hi, i) for i in range(L)])
    
    # Obtain ground state (exact diagonalization)
    exact_energy, ground_state = compute_ground_state(H, hi)
    
    # Calculate energy using QMC sampling
    qmc_energy, qmc_error = qmc_energy_estimate(ground_state, L, n_samples,
                                                diag_multiplier=J,
                                                offdiag_multiplier=h)
    
    return exact_energy, qmc_energy, qmc_error

if __name__ == '__main__':
    np.random.seed(42)
    
    print("====== Transverse Field Ising Model Simulation ======")
    L = 8        # System size
    J = 1.0      # Coupling
    h = 1.0      # Transverse field strength
    n_samples = 100000
    exact_energy, qmc_energy, qmc_error = tfim_model_sign(L, J, h, n_samples)
    print(f"Exact diagonalization energy: {exact_energy:.8f}")
    print(f"QMC estimated energy: {qmc_energy:.8f} ± {qmc_error:.8f}")
    print(f"Relative error: {abs(qmc_energy - exact_energy) / abs(exact_energy) * 100:.2f}%\n")



def heisenberg_model_signproblem(L=8, J=1.0, n_samples=100000):
    """
    QMC simulation of antiferromagnetic Heisenberg model, demonstrating the sign problem
    Hamiltonian: H = J ∑ (σ_i^xσ_{i+1}^x + σ_i^yσ_{i+1}^y + σ_i^zσ_{i+1}^z)

    Args:
        L: System size
        J: Spin coupling strength (J > 0 for antiferromagnetic, prone to sign problems)
        n_samples: Number of Monte Carlo samples
        
    Returns:
        exact_energy: Energy from exact diagonalization
        qmc_energy: Energy from QMC estimation
        qmc_error: Error in QMC estimation
    """
    # Define the Hilbert space (spin 1/2)
    hi = nk.hilbert.Spin(s=1/2, N=L)
    
    # Construct the Heisenberg Hamiltonian
    H = 0
    for i in range(L):
        H += J * (sigmax(hi, i) * sigmax(hi, (i + 1) % L) +
                  sigmay(hi, i) * sigmay(hi, (i + 1) % L) +
                  sigmaz(hi, i) * sigmaz(hi, (i + 1) % L))
    
    # Obtain ground state (exact diagonalization), note: ground state may contain both positive and negative coefficients
    exact_energy, ground_state = compute_ground_state(H, hi)
    
    # Calculate energy using QMC sampling
    # For the Heisenberg model, off-diagonal term coefficients are the same as J
    qmc_energy, qmc_error = qmc_energy_estimate(ground_state, L, n_samples,
                                                diag_multiplier=J,
                                                offdiag_multiplier=J)
    
    return exact_energy, qmc_energy, qmc_error

if __name__ == '__main__':
    np.random.seed(42)

    print("====== Antiferromagnetic Heisenberg Model Simulation ======")
    exact_energy, qmc_energy, qmc_error = heisenberg_model_signproblem(L, J, n_samples)
    print(f"Exact diagonalization energy: {exact_energy:.8f}")
    print(f"QMC estimated energy: {qmc_energy:.8f} ± {qmc_error:.8f}")
    print(f"Relative error: {abs(qmc_energy - exact_energy) / abs(exact_energy) * 100:.2f}%")



# Set system parameters
L = 16

# Create a one-dimensional periodic chain (single-dimension lattice)
graph_chain = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)
hilbert_chain = nk.hilbert.Spin(s=1/2, total_sz=0, N=graph_chain.n_nodes)

# Heisenberg model: nearest-neighbor interactions with coupling constant J=1.0
ham_heisenberg = nk.operator.Heisenberg(hilbert=hilbert_chain, graph=graph_chain)

# Create sampler
sampler = nk.sampler.MetropolisExchange(hilbert=hilbert_chain, graph=graph_chain)

# Define optimizer and stochastic reconfiguration (SR) parameters
optimizer = nk.optimizer.Sgd(learning_rate=0.01)
sr = nk.optimizer.SR(diag_shift=0.1)
n_iter = 600
n_samples = 3200


def run_vmc_simulation(hamiltonian, hilbert, sampler, model, optimizer, sr, n_iter, n_samples, out_label):
    """
    Encapsulates VMC simulation process, returns final energy and relative error
  
    Args:
        hamiltonian: NetKet defined Hamiltonian
        hilbert: Hilbert space
        sampler: Sampler object
        model: Trial wave function model (e.g., Jastrow, RBM, or custom ViT model)
        optimizer: Optimizer object
        sr: Stochastic Reconfiguration preprocessor
        n_iter: Number of iterations
        n_samples: Number of Monte Carlo samples
        out_label: Log file save label
        
    Returns:
        final_energy: Mean energy after VMC optimization
        relative_error: Relative error (compared to exact energy)
        exact_energy: Energy from exact diagonalization (Lanczos ED)
    """
    # Create variational state (MCState)
    vs = nk.vqs.MCState(sampler, model, n_samples=n_samples)
    # Construct VMC object
    vmc_driver = nk.VMC(
        hamiltonian=hamiltonian,
        optimizer=optimizer,
        preconditioner=sr,
        variational_state=vs
    )
    # Calculate exact energy (Lanczos diagonalization, only suitable for small system sizes)
    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)
    exact_energy = evals[0]
    
    # Iterative optimization
    vmc_driver.run(n_iter, out=out_label)
    
    final_energy = vs.expect(hamiltonian).mean
    relative_error = abs(final_energy - exact_energy) / abs(exact_energy)
    
    return final_energy, relative_error, exact_energy

# Solve using Jastrow model
model_jastrow = nk.models.Jastrow()
final_energy_jastrow, rel_err_jastrow, exact_energy_heis = run_vmc_simulation(
    ham_heisenberg, hilbert_chain, sampler, model_jastrow,
    optimizer, sr, n_iter, n_samples, out_label="Jastrow"
)
print("【Heisenberg Model - Jastrow】")
print(f"Exact energy: {exact_energy_heis:.8f}")
print(f"VMC final energy: {final_energy_jastrow:.8f}")
print(f"Relative error: {rel_err_jastrow*100:.2f}%\n")


# Solve using RBM model (alpha=1)
model_rbm = nk.models.RBM(alpha=1)
final_energy_rbm, rel_err_rbm, _ = run_vmc_simulation(
    ham_heisenberg, hilbert_chain, sampler, model_rbm,
    optimizer, sr, n_iter, n_samples, out_label="RBM"
)
print("【Heisenberg Model - RBM】")
print(f"Exact energy: {exact_energy_heis:.8f}")
print(f"VMC final energy: {final_energy_rbm:.8f}")
print(f"Relative error: {rel_err_rbm*100:.2f}%\n")

import json
import matplotlib.pyplot as plt

# Read Jastrow log data
with open("Jastrow.log", "r") as f:
    data_Jastrow = json.load(f)
iters_Jastrow = data_Jastrow["Energy"]["iters"]
# If energy is in complex format, take its real part
energy_Jastrow = data_Jastrow["Energy"]["Mean"]["real"]

# Read RBM log data
with open("RBM.log", "r") as f:
    data_RBM = json.load(f)
iters_RBM = data_RBM["Energy"]["iters"]
energy_RBM = data_RBM["Energy"]["Mean"]

# Plot figure
plt.figure(figsize=(8, 6))
plt.plot(iters_Jastrow, energy_Jastrow, label="Jastrow")
plt.plot(iters_RBM, energy_RBM, label="RBM")
plt.axhline(y=exact_energy_heis, xmin=0,
                xmax=iters_Jastrow[-1], linewidth=2, color='k', label='Exact')
plt.xlabel("Iteration")
plt.ylabel("Energy")
plt.title("Energy vs Iterations")
plt.legend()
plt.grid(True)
plt.show()

import jax
import jax.numpy as jnp
from flax import linen as nn
from typing import Sequence
import netket as nk
import numpy as np
import matplotlib.pyplot as plt
import time
import json

# Define core modules for Vision Transformer (ViT) model
def custom_uniform(scale=1e-2, dtype=jnp.float_):
    """
    Custom weight initialization function for neural network parameters
    """
    def init(key, shape, dtype=dtype):
        dtype = jax.dtypes.canonicalize_dtype(dtype)
        return jax.random.uniform(key, shape, dtype, minval=-scale, maxval=scale)
    return init

@jax.jit
def log_cosh(x):
    """
    Log-cosh activation function, a smooth approximation of ReLU
    that works well for quantum wave function representation
    """
    sgn_x = -2 * jnp.signbit(x.real) + 1
    x = x * sgn_x
    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)

@jax.jit
def attention(J, values, shift):
    """
    Custom attention mechanism for quantum systems
    """
    # Cyclic shift values, then weighted sum by J
    values = jnp.roll(values, shift, axis=0)
    return jnp.sum(J * values, axis=0)

class EncoderBlock(nn.Module):
    """
    Transformer encoder block with custom attention for quantum systems
    """
    d_model: int  # Model dimension
    h: int        # Number of attention heads
    L: int        # Number of sequence blocks
    b: int        # Size per block

    def setup(self):
        scale = (3.0 * 0.7 / self.L) ** 0.5
        # Value projections (real and imaginary parts)
        self.v_projR = nn.Dense(self.d_model, param_dtype=jnp.float64,
                                kernel_init=jax.nn.initializers.variance_scaling(0.3, "fan_in", "uniform"),
                                bias_init=nn.initializers.zeros)
        self.v_projI = nn.Dense(self.d_model, param_dtype=jnp.float64,
                                kernel_init=jax.nn.initializers.variance_scaling(0.3, "fan_in", "uniform"),
                                bias_init=nn.initializers.zeros)
        # Attention weights (complex)
        self.JR = self.param("JR", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)
        self.JI = self.param("JI", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)
        # Output projection
        self.W0R = nn.Dense(self.d_model, param_dtype=jnp.float64,
                            kernel_init=jax.nn.initializers.variance_scaling(0.065, "fan_in", "uniform"),
                            bias_init=nn.initializers.zeros)
        self.W0I = nn.Dense(self.d_model, param_dtype=jnp.float64,
                            kernel_init=jax.nn.initializers.variance_scaling(0.065, "fan_in", "uniform"),
                            bias_init=nn.initializers.zeros)

    def __call__(self, x):
        # Construct complex attention weights
        J = self.JR + 1j * self.JI
        # Project values and convert to complex form, reshape to (L, h, dim)
        x = self.v_projR(x).reshape(self.L, self.h, -1) + 1j * self.v_projI(x).reshape(self.L, self.h, -1)
        # Apply attention for each position
        x = jax.vmap(attention, (None, None, 0))(J, x, jnp.arange(self.L))
        x = x.reshape(self.L, -1)
        # Output projection
        x = self.W0R(x) + 1j * self.W0I(x)
        return log_cosh(x)

class TransformerEnc(nn.Module):
    """
    Full Transformer encoder for quantum wave function representation
    """
    d_model: int  # Model dimension
    h: int        # Number of attention heads
    L: int        # Number of sequence blocks
    b: int        # Size per block

    def setup(self):
        self.encoder = EncoderBlock(self.d_model, self.h, self.L, self.b)

    def __call__(self, x):
        # Reshape input into blocks: [num_blocks, block_size, batch_size]
        x = x.reshape(x.shape[0], -1, self.b)
        x = jax.vmap(self.encoder)(x)
        # Pool features from all blocks (sum)
        return jnp.sum(x, axis=(1, 2))

# Create a one-dimensional chain lattice (allowing next-nearest-neighbor interactions)
lattice_j1j2 = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)
hilbert_j1j2 = nk.hilbert.Spin(s=1/2, N=lattice_j1j2.n_nodes, total_sz=0)

# Define J1-J2 Heisenberg model: J1=1.0, J2=0.5
ham_j1j2 = nk.operator.Heisenberg(hilbert=hilbert_j1j2, graph=lattice_j1j2, J=[1.0, 0.5])
sampler_j1j2 = nk.sampler.MetropolisExchange(hilbert=hilbert_j1j2, graph=lattice_j1j2)


# Solve the J1-J2 model using the RBM model
final_energy_rbm_j1j2, rel_err_rbm_j1j2, exact_energy_j1j2 = run_vmc_simulation(
    ham_j1j2, hilbert_j1j2, sampler_j1j2, model_rbm,
    optimizer, sr, n_iter, n_samples, out_label="RBM_J1J2"
)
print("【J1-J2 Model - RBM】")
print(f"Exact energy: {exact_energy_j1j2:.8f}")
print(f"VMC final energy: {final_energy_rbm_j1j2:.8f}")
print(f"Relative error: {rel_err_rbm_j1j2*100:.2f}%\n")

# ViT model parameters
heads = 8
feature_factor = 1
batch_size = 4
d_model = feature_factor * heads

# Build ViT model instance, note input size should match Hilbert space dimensions
vit_model = TransformerEnc(d_model=d_model, h=heads, L=L // batch_size, b=batch_size)

# For ViT, we need to use custom samplers and initialize parameters
seed = 0
sampler_j1j2_vit = nk.sampler.MetropolisExchange(hilbert=hilbert_j1j2, graph=lattice_j1j2)
key = jax.random.PRNGKey(seed)
params = vit_model.init(key, jnp.zeros((1, hilbert_j1j2.size)))
vs_vit = nk.vqs.MCState(sampler_j1j2_vit, vit_model, n_samples=n_samples, variables=params)

# Construct VMC optimizer object
vmc_driver_vit = nk.VMC(
    hamiltonian=ham_j1j2,
    optimizer=optimizer,
    preconditioner=sr,
    variational_state=vs_vit
)

# Run ViT model optimization
vmc_driver_vit.run(n_iter, out="ViT_J1J2")
final_energy_vit = vs_vit.expect(ham_j1j2).mean
evals_vit = nk.exact.lanczos_ed(ham_j1j2, compute_eigenvectors=False)
exact_energy_j1j2_vit = evals_vit[0]
rel_err_vit = abs(final_energy_vit - exact_energy_j1j2_vit) / abs(exact_energy_j1j2_vit)

print("【J1-J2 Model - ViT】")
print(f"Exact energy: {exact_energy_j1j2_vit:.8f}")
print(f"VMC final energy: {final_energy_vit:.8f}")
print(f"Relative error: {rel_err_vit*100:.2f}%\n")

import json
import matplotlib.pyplot as plt

# Read RBM log data
with open("RBM_J1J2.log", "r") as f:
    data_RBM = json.load(f)
iters_RBM = data_RBM["Energy"]["iters"]
# If energy is in complex format, take its real part
energy_RBM = data_RBM["Energy"]["Mean"]

# Read ViT log data
with open("ViT_J1J2.log", "r") as f:
    data_ViT = json.load(f)
iters_ViT = data_ViT["Energy"]["iters"]
energy_ViT = data_ViT["Energy"]["Mean"]

if isinstance(energy_ViT, dict) and "real" in energy_ViT:
    energy_ViT = energy_ViT["real"]

# Plot figure
plt.figure(figsize=(8, 6))
plt.plot(iters_RBM, energy_RBM, label="RBM")
plt.plot(iters_ViT, energy_ViT, label="ViT")
plt.xlabel("Iteration")
plt.ylabel("Energy")
plt.axhline(y=exact_energy_j1j2_vit, xmin=0,
                xmax=iters_Jastrow[-1], linewidth=2, color='k', label='Exact')
plt.title("Energy vs Iterations")
plt.legend()
plt.grid(True)
plt.ylim(top=10, bottom=-25)
plt.show()